package mobiarmy.shop;

import java.sql.Connection;
import java.sql.PreparedStatement;
import java.sql.ResultSet;
import java.sql.SQLException;
import java.util.ArrayList;

import mobiarmy.db.DBManager;
import mobiarmy.server.LinhTinh;

/**
 *
 * <AUTHOR> Tú
 */
public class ShopLinhTinh {

    
    public static LinhTinh entrys[];
    
    public static void loadShopLinhTinh() throws SQLException {
        Connection connection = DBManager.getInstance().getConnection(DBManager.SERVER);
        PreparedStatement statement = connection.prepareStatement("SELECT * FROM shop_linhtinh");
        ResultSet rs = statement.executeQuery();
        try {
            ArrayList<LinhTinh> tempList = new ArrayList<>();
            while (rs.next()) {
                LinhTinh linhTinh = LinhTinh.get(rs.getInt("id")).deepCopy();
                linhTinh.isSelectNum = rs.getBoolean("isSelectNum");
                tempList.add(linhTinh);
            }
            entrys = tempList.toArray(new LinhTinh[tempList.size()]);
        } finally {
            connection.close();
            rs.close();
            statement.close();
        }
    }
    
    public static LinhTinh get(int id) {
        for (LinhTinh entry : entrys) {
            if (entry.id == id) {
                return entry;
            }
        }
        return null;
    }
}
