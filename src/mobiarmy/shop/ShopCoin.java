package mobiarmy.shop;

import mobiarmy.db.DBManager;
import mobiarmy.server.LinhTinh;
import org.json.simple.JSONArray;
import org.json.simple.JSONValue;

import java.sql.Connection;
import java.sql.PreparedStatement;
import java.sql.ResultSet;
import java.sql.SQLException;
import java.util.ArrayList;

/**
 * Created by khiem on 9/22/2025.
 *
 * <AUTHOR>
 */
public class ShopCoin {
    public static LinhTinh entrys[];

    public static void loadShopCoin() throws SQLException {
        Connection connection = DBManager.getInstance().getConnection(DBManager.SERVER);
        PreparedStatement statement = connection.prepareStatement("SELECT * FROM shopcoin");
        ResultSet rs = statement.executeQuery();
        try {
            ArrayList<LinhTinh> tempList = new ArrayList<>();
            while (rs.next()) {
                LinhTinh linhTinh = LinhTinh.get(rs.getInt("id")).deepCopy();
                linhTinh.isSelectNum = rs.getBoolean("isSelectNum");
                linhTinh.coin = rs.getInt("coin");
                linhTinh.date=rs.getByte("date");
                String abilityJson = rs.getString("ability");
                if (abilityJson != null && !abilityJson.trim().isEmpty() && !abilityJson.equals("null")) {
                    JSONArray abilityArray = (JSONArray) JSONValue.parse(abilityJson);
                    if (abilityArray != null) {
                        linhTinh.ability = new short[abilityArray.size()];
                        for (int j = 0; j < abilityArray.size(); j++) {
                            linhTinh.ability[j] = Short.parseShort(abilityArray.get(j).toString());
                        }
                    }
                }
                tempList.add(linhTinh);
            }
            entrys = tempList.toArray(new LinhTinh[tempList.size()]);
        } finally {
            connection.close();
            rs.close();
            statement.close();
        }
    }

    public static LinhTinh get(int id) {
        for (LinhTinh entry : entrys) {
            if (entry.id == id) {
                return entry;
            }
        }
        return null;
    }
}
