package mobiarmy.shop;

import mobiarmy.db.DBManager;
import mobiarmy.server.Equip;
import org.json.simple.JSONArray;
import org.json.simple.JSONValue;

import java.sql.Connection;
import java.sql.PreparedStatement;
import java.sql.ResultSet;
import java.sql.SQLException;
import java.util.ArrayList;

/**
 *
 * <AUTHOR> Tú
 */
public class ShopEquipment {
    
    public static Equip entrys[];
    
    public static void loadShopEquipment() throws SQLException {
        Connection connection = DBManager.getInstance().getConnection(DBManager.SERVER);
        PreparedStatement statement = connection.prepareStatement("SELECT * FROM shop_equipment");
        ResultSet rs = statement.executeQuery();
        try {
            ArrayList<Equip> tempList = new ArrayList<>();
            while (rs.next()) {
                Equip equip = Equip.get(rs.getByte("glassID"), rs.getByte("equipId")).deepCopy();
                equip.name = rs.getString("name");
                equip.xu = rs.getInt("xu");
                equip.luong = rs.getInt("luong");
                
                // Parse inv_ability array
                String invAbilityJson = rs.getString("inv_ability");
                if (invAbilityJson != null && !invAbilityJson.trim().isEmpty() && !invAbilityJson.equals("null")) {
                    JSONArray abilityArray = (JSONArray) JSONValue.parse(invAbilityJson);
                    if (abilityArray != null) {
                        equip.inv_ability = new byte[abilityArray.size()];
                        for (int j = 0; j < abilityArray.size(); j++) {
                            equip.inv_ability[j] = Byte.parseByte(abilityArray.get(j).toString());
                        }
                    }
                }
                
                // Parse inv_percen array
                String invPercenJson = rs.getString("inv_percen");
                if (invPercenJson != null && !invPercenJson.trim().isEmpty() && !invPercenJson.equals("null")) {
                    JSONArray percenArray = (JSONArray) JSONValue.parse(invPercenJson);
                    if (percenArray != null) {
                        equip.inv_percen = new byte[percenArray.size()];
                        for (int j = 0; j < percenArray.size(); j++) {
                            equip.inv_percen[j] = Byte.parseByte(percenArray.get(j).toString());
                        }
                    }
                }
                tempList.add(equip);
            }
            entrys = tempList.toArray(new Equip[tempList.size()]);
        } finally {
            connection.close();
            rs.close();
            statement.close();
        }
    }
    
    public static ArrayList<Equip> generate(byte glassID) {
        ArrayList<Equip> equips = new ArrayList<>();
        for (Equip entry : entrys) {
            if (entry.glassID == glassID) {
                equips.add(entry);
            }
        }
        return equips;
    }
    
}
